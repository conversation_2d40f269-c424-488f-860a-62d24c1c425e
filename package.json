{"name": "web", "private": true, "version": "0.0.1", "type": "module", "packageManager": "bun@1.2.7", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "test:unit": "vitest", "test": "npm run test:unit -- --run"}, "devDependencies": {"@babel/helper-compilation-targets": "^7.27.0", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/adapter-vercel": "^5.7.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.4", "@types/jquery": "^3.5.32", "estree-walker": "^2", "jsdom": "^26.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "vite": "^6.2.5", "vitest": "^3.0.0"}, "dependencies": {"@tanstack/svelte-query": "^5.79.0", "axios": "^1.8.4", "svelte-i18n": "^4.0.1"}}