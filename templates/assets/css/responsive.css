/* Max width 767px */
@media only screen and (max-width: 767px) {
  .ptb-100 {
    padding-top: 50px;
    padding-bottom: 50px;
  }
  .pt-100 {
    padding-top: 50px;
  }
  .pb-100 {
    padding-bottom: 50px;
  }
  .pb-70 {
    padding-bottom: 20px;
  }
  p {
    font-size: 15px;
  }
  .top-header .header-left-content {
    text-align: center;
    display: block;
    margin-bottom: 15px;
  }
  .top-header .header-right-content {
    text-align: center;
    float: unset;
  }
  .top-header .header-right-content .my-account li .my-account-link {
    text-align: right;
  }
  .banner-content {
    text-align: center;
  }
  .banner-content .top-title {
    font-size: 13px;
    margin-bottom: 15px;
  }
  .banner-content h1 {
    font-size: 35px;
    color: #ffffff;
  }
  .banner-content p {
    color: #ffffff;
    margin-bottom: 25px;
  }
  .banner-area {
    height: 100%;
    z-index: 1;
    padding-top: 60px;
    padding-bottom: 60px;
    background-position: center center;
    background-size: cover;
  }
  .banner-area::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: #000000;
    opacity: 0.9;
    z-index: -1;
  }
  .default-btn {
    font-size: 15px;
    padding: 12px 12px;
  }
  .about-content {
    text-align: center;
    margin-bottom: 30px;
  }
  .about-content h2 {
    font-size: 30px;
  }
  .about-year-content {
    text-align: center;
    margin-right: 0;
  }
  .about-year-content img {
    margin-bottom: 30px;
  }
  .about-year-content h3 {
    position: unset;
    text-align: center;
    margin-top: -11px;
    margin-bottom: -5px;
  }
  .choose-us-img {
    text-align: center;
    margin-bottom: 30px;
  }
  .choose-us-tab {
    margin-top: 20px;
  }
  .choose-us-tab h2 {
    text-align: center;
    font-size: 30px;
    margin-bottom: 30px;
  }
  .tabs li {
    font-size: 14px;
    padding: 10px 18px;
  }
  .tab_content {
    text-align: center;
  }
  .tab_content .tabs_item .choose-tab-item h3 {
    font-size: 22px;
  }
  .section-title {
    margin-bottom: 30px;
  }
  .section-title h2 {
    font-size: 30px;
  }
  .single-services-box {
    padding: 15px;
  }
  .single-services-box img {
    margin-bottom: 20px;
  }
  .single-services-box h3 {
    font-size: 21px;
  }
  .logistics-solutions {
    text-align: center;
    margin-top: -7px;
    margin-bottom: 30px;
  }
  .logistics-solutions h2 {
    font-size: 30px;
    margin-bottom: 20px;
  }
  .logistics-solutions ul {
    margin-left: 0;
    padding: 0;
  }
  .logistics-solutions ul li {
    padding-right: 15px;
  }
  .logistics-solutions ul li i {
    position: unset;
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 35px;
    margin-bottom: 10px;
  }
  .logistics-solutions-img {
    height: 300px;
  }
  .logistics-solutions-img .choose-us-shape-1 {
    left: 0;
  }
  .logistics-solutions-img .choose-us-shape-2 {
    left: 0;
  }
  .single-counter {
    text-align: center;
  }
  .single-counter i {
    position: unset;
    top: 0;
    margin-bottom: 20px;
    display: block;
  }
  .single-counter .count-title {
    margin-right: 0;
  }
  .single-counter .count-title h2 {
    font-size: 40px;
    margin-top: -8px;
  }
  .single-counter .count-title h2 .target {
    margin-right: -6px;
    font-size: 35px;
  }
  .single-counter .count-title h4 {
    font-size: 16px;
  }
  .single-counter::before {
    display: none;
  }
  .request-quote-content {
    margin-bottom: 0;
  }
  .request-quote-content .top-title {
    text-align: center;
  }
  .request-quote-content h2 {
    font-size: 30px;
    margin-bottom: 25px;
    text-align: center;
  }
  .request-quote-content .request-quote-from {
    padding: 20px;
    margin-right: 0;
    background-color: #ffffff;
    box-shadow: 0 0 20px 3px rgba(0, 0, 0, 0.05);
  }
  .request-quote-img {
    margin-bottom: 30px;
    height: 300px;
  }
  .single-team-member {
    padding: 15px;
    padding-bottom: 0;
  }
  .single-team-member .team-content {
    padding: 15px;
  }
  .single-team-member .team-content h3 {
    font-size: 18px;
  }
  .team-area .owl-dots {
    margin-top: 0 !important;
  }
  .testimonials-content {
    text-align: center;
    margin-right: 0;
  }
  .testimonials-content p {
    font-size: 15px;
  }
  .testimonials-img .testimonials-title {
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
  }
  .testimonials-area .owl-nav {
    position: unset;
    line-height: 1;
    margin-bottom: -6px;
  }
  .single-blog-box {
    padding: 15px;
  }
  .single-blog-box img {
    margin-bottom: 15px;
  }
  .single-blog-box h3 {
    margin-bottom: 10px;
    font-size: 22px;
  }
  .single-blog-box p {
    margin-bottom: 12px;
  }
  .single-footer-widget h3 {
    font-size: 22px;
  }
  /* Home Two */
  .header-area.header-area-style-two {
    position: unset;
  }
  .banner-area.banner-area-style-two {
    height: 100%;
  }
  .banner-area.banner-area-style-two::before {
    display: none;
  }
  .banner-area.banner-area-style-two .banner-content {
    margin-top: 0;
  }
  .banner-area.banner-area-style-two .banner-content h1 {
    color: #1f2428;
  }
  .single-success {
    padding-right: 0;
    padding-left: 0;
  }
  .choose-img .your-of {
    position: unset;
    max-width: 100%;
    margin-top: 30px;
  }
  .faq-accordion h2 {
    font-size: 30px;
    text-align: center;
    margin-bottom: 30px;
  }
  .logistics-solutions-area.logistics-solutions-area-style-two .logistics-solutions-img.bg-2 .video-button {
    bottom: -175px;
    right: 30px;
  }
  .logistics-solutions-img.bg-2 {
    margin-bottom: 0;
    margin-top: 30px;
  }
  .testimonials-img {
    margin-bottom: 15px;
  }
  .single-testimonials-box {
    padding: 20px;
  }
  .single-testimonials-box ul {
    position: unset;
    margin-bottom: 10px;
  }
  .testimonials-area-style-two .owl-dots {
    margin-top: 0 !important;
  }
  .request-quote-area.request-quote-area-style-two .request-quote-content {
    padding: 20px;
  }
  .request-quote-area.request-quote-area-style-two .request-quote-content .request-quote-from {
    box-shadow: none;
    background-color: transparent;
  }
  .faq-accordion .accordion .accordion-item {
    padding-left: 40px;
  }
  .faq-accordion .accordion .accordion-title i {
    left: -24px;
  }
  /* Home Three */
  .header-area.header-area-style-three {
    position: unset;
  }
  .navbar-area.navbar-area-style-three {
    border-bottom: 1px solid #e5e9ff;
  }
  .banner-area.banner-area-style-three {
    position: relative;
    z-index: 1;
  }
  .banner-area.banner-area-style-three::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    opacity: 0.6;
    z-index: -1;
  }
  .about-us-content {
    text-align: center;
  }
  .about-us-content h2 {
    font-size: 30px;
    margin-bottom: 20px;
  }
  .about-us-content .default-btn {
    margin-top: 10px;
  }
  .logistics-solutions-area.logistics-solutions-area-style-three .logistics-solutions ul li {
    padding-right: 0;
  }
  .logistics-solutions-area.logistics-solutions-area-style-three .logistics-solutions ul li i::after {
    right: 0;
    left: 0;
    margin: auto;
  }
  .single-pricing-box .pricing-title h3 {
    font-size: 22px;
  }
  .single-pricing-box .pricing-title h1 {
    font-size: 40px;
  }
  .single-pricing-box .pricing-title h1 sub span {
    font-size: 25px;
  }
  .blog-area.blog-area-style-three .single-blog-box .single-blog-content {
    padding: 15px;
  }
  /* Home Four */
  .header-area.header-area-style-four {
    position: unset;
  }
  .banner-area.banner-area-style-four .banner-content h1 {
    color: #ffffff;
  }
  /* Inner Page */
  .page-title-area {
    padding-top: 60px;
    padding-bottom: 60px;
    position: relative;
    z-index: 1;
    text-align: center;
  }
  .page-title-area::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    opacity: 0.6;
  }
  .page-title-area .page-title-content h2 {
    font-size: 35px;
    margin-bottom: 5px;
  }
  .services-details-area .sidebar-widget {
    margin-left: 0;
  }
  .services-details-content.content-1 h3 {
    font-size: 25px;
  }
  .services-details-content.content-3 h3 {
    font-size: 22px;
  }
  .services-details-content.content-4 h3 {
    font-size: 22px;
  }
  .services-details-content.content-5 h3 {
    font-size: 22px;
  }
  .sidebar-widget h3 {
    font-size: 22px;
  }
  .user-area .log-in-50 {
    margin-bottom: 50px;
  }
  .user-area .user-form-content h3 {
    font-size: 20px;
  }
  .user-area .user-form-content .user-form {
    padding: 20px;
  }
  .user-area .user-form-content .user-form .login-action .forgot-login {
    float: unset;
    margin-top: 10px;
  }
  .coming-soon-content {
    max-width: 100%;
  }
  .coming-soon-content h1 {
    font-size: 40px;
    line-height: 1.1;
  }
  .coming-soon-content p {
    font-size: 13px;
    margin-top: 15px;
  }
  .coming-soon-content #timer {
    margin-top: 35px;
  }
  .coming-soon-content #timer div {
    font-size: 38px;
    width: auto;
    height: auto;
    padding-top: 0;
    background-color: transparent;
    margin-right: 9px;
    margin-left: 9px;
  }
  .coming-soon-content #timer div span {
    font-size: 13px;
    margin-top: -5px;
  }
  .coming-soon-content #timer div::before {
    display: none;
  }
  .coming-soon-content #timer div::after {
    display: none;
  }
  .coming-soon-content .newsletter-form {
    margin-top: 30px;
  }
  .coming-soon-content .newsletter-form .input-newsletter {
    height: 55px;
    padding-right: 13px;
    font-size: 15px;
  }
  .coming-soon-content .newsletter-form button {
    position: relative;
    height: auto;
    padding: 14px 30px;
    font-size: 14px;
    margin-top: 15px;
  }
  .error-area {
    padding: 70px 0;
    height: 100%;
  }
  .error-area .error-content p {
    margin: 15px 0 20px;
    font-size: 15px;
  }
  .error-area .error-content h1 {
    font-size: 110px;
    margin-top: -22px;
  }
  .sidebar-widget {
    margin-right: 0;
  }
  .sidebar-widget h3 {
    font-size: 22px;
  }
  .pagination-area {
    margin-bottom: 35px;
  }
  .blog-page-area .single-blog-box h3 {
    font-size: 22px;
  }
  .blog-details-content.content-3 h3 {
    font-size: 25px;
  }
  .blog-details-content.content-4 blockquote {
    padding: 20px;
  }
  .blog-details-content.content-4 blockquote i {
    display: none;
  }
  .blog-details-content.content-4 blockquote p {
    font-size: 16px;
  }
  .blog-details-content.content-6 {
    text-align: center;
  }
  .blog-details-content.content-6 ul li span {
    font-size: 15px;
  }
  .blog-details-content.content-6 ul li a {
    margin-right: 3px;
  }
  .blog-details-content.content-6 .social-icon {
    float: unset;
    margin-top: 10px;
  }
  .blog-details-content.content-6 .social-icon li span {
    font-size: 15px;
  }
  .blog-details-content.content-7 {
    text-align: center;
    padding: 20px;
    padding-right: 20px;
  }
  .blog-details-content.content-7 img {
    position: unset;
    margin-bottom: 20px;
  }
  .blog-details-content.content-7 h3 {
    font-size: 22px;
  }
  .blog-details-content.content-8 {
    margin-bottom: 35px;
  }
  .blog-details-content.content-8 h3 {
    font-size: 22px;
  }
  .single-contact-info::before {
    display: none;
  }
  .single-contact-info .count-title {
    margin-right: 0;
  }
  .single-contact-info i {
    position: unset;
    display: block;
    margin-bottom: 15px;
    text-align: center;
  }
  .main-contact-area #contactForm {
    padding: 20px;
  }
  .main-contact-area .form-group {
    margin-bottom: 20px;
  }
  .sidebar-widget.recent-post {
    margin-bottom: 15px;
  }
  .single-blog-box {
    text-align: center;
  }
  .about-us-content {
    margin: auto;
  }
  .blog-details-content.content-9 .comments h3 {
    font-size: 22px;
  }
  .blog-details-content.content-9 .comments ul li {
    padding-right: 0;
  }
  .blog-details-content.content-9 .comments ul li img {
    position: unset;
  }
  .single-testimonials-box {
    text-align: center;
  }
  .single-testimonials-box .testimonials-img {
    margin-bottom: 12px;
  }
  .single-testimonials-box .testimonials-img .testimonials-mane {
    position: unset;
    margin-top: 15px;
  }
  .testimonials-area-style-two .owl-carousel .owl-item img {
    margin: auto;
  }
  .single-testimonials p {
    font-size: 16px;
  }
}
/* Min width 349px to Max width 767px */
@media only screen and (min-width: 349px) and (max-width: 767px) {
  .sidebar-widget.recent-post ul li {
    height: 100px;
  }
  .sidebar-widget.recent-post ul li:last-child {
    padding-bottom: 0 !important;
  }
}
/* Min width 576px to Max width 767px */
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .about-area .container-fluid {
    max-width: 540px;
  }
}
/* Min width 768px to Max width 991px */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ptb-100 {
    padding-top: 50px;
    padding-bottom: 50px;
  }
  .pt-100 {
    padding-top: 50px;
  }
  .pb-100 {
    padding-bottom: 50px;
  }
  .pb-70 {
    padding-bottom: 20px;
  }
  p {
    font-size: 15px;
  }
  .banner-content {
    text-align: center;
  }
  .banner-content .top-title {
    font-size: 13px;
    margin-bottom: 15px;
  }
  .banner-content h1 {
    font-size: 35px;
    color: #ffffff;
  }
  .banner-content p {
    color: #ffffff;
    margin-bottom: 25px;
  }
  .banner-area {
    height: 100%;
    z-index: 1;
    padding-top: 60px;
    padding-bottom: 60px;
    background-position: center center;
    background-size: cover;
  }
  .banner-area::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: #000000;
    opacity: 0.9;
    z-index: -1;
  }
  .default-btn {
    font-size: 15px;
    padding: 12px 12px;
  }
  .about-content {
    text-align: center;
    margin-bottom: 30px;
  }
  .about-content h2 {
    font-size: 30px;
  }
  .about-year-content {
    text-align: center;
    margin-right: 0;
  }
  .about-year-content h3 {
    position: relative;
    text-align: center;
    margin-top: -11px;
    margin-bottom: -5px;
  }
  .choose-us-img {
    text-align: center;
    margin-bottom: 30px;
  }
  .choose-us-img .choose-us-shape-1 {
    left: 0;
  }
  .choose-us-img .choose-us-shape-2 {
    left: 0;
  }
  .choose-us-tab h2 {
    text-align: center;
    font-size: 30px;
    margin-bottom: 30px;
  }
  .tabs li {
    font-size: 14px;
    padding: 10px 18px;
  }
  .tab_content {
    text-align: center;
  }
  .tab_content .tabs_item .choose-tab-item h3 {
    font-size: 22px;
  }
  .section-title {
    margin-bottom: 30px;
  }
  .section-title h2 {
    font-size: 30px;
  }
  .single-services-box {
    padding: 15px;
  }
  .single-services-box img {
    margin-bottom: 15px;
  }
  .single-services-box h3 {
    font-size: 21px;
  }
  .logistics-solutions {
    text-align: center;
    margin-top: -7px;
    margin-bottom: 30px;
  }
  .logistics-solutions h2 {
    font-size: 30px;
    margin-bottom: 20px;
  }
  .logistics-solutions ul {
    margin-left: 0;
    padding: 0;
  }
  .logistics-solutions ul li {
    padding-right: 15px;
  }
  .logistics-solutions ul li i {
    position: unset;
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 35px;
    margin-bottom: 10px;
  }
  .logistics-solutions-img {
    height: 300px;
  }
  .single-counter {
    text-align: center;
  }
  .single-counter i {
    position: unset;
    top: 0;
    margin-bottom: 20px;
    display: block;
  }
  .single-counter .count-title {
    margin-right: 0;
  }
  .single-counter .count-title h2 {
    font-size: 40px;
    margin-top: -8px;
  }
  .single-counter .count-title h2 .target {
    margin-right: -6px;
    font-size: 35px;
  }
  .single-counter .count-title h4 {
    font-size: 16px;
  }
  .single-counter::before {
    display: none;
  }
  .request-quote-content {
    margin-bottom: 0;
  }
  .request-quote-content .top-title {
    text-align: center;
  }
  .request-quote-content h2 {
    font-size: 30px;
    margin-bottom: 25px;
    text-align: center;
  }
  .request-quote-content .request-quote-from {
    padding: 20px;
    margin-right: 0;
    background-color: #ffffff;
    box-shadow: 0 0 20px 3px rgba(0, 0, 0, 0.05);
  }
  .request-quote-img {
    margin-bottom: 30px;
    height: 300px;
  }
  .single-team-member {
    padding: 15px;
    padding-bottom: 0;
  }
  .team-area .owl-dots {
    margin-top: 0 !important;
  }
  .testimonials-content {
    text-align: center;
    margin-right: 0;
  }
  .testimonials-content p {
    font-size: 15px;
  }
  .testimonials-img .testimonials-title {
    left: 0;
    right: 0;
    bottom: 0;
    text-align: center;
  }
  .testimonials-area .owl-nav {
    position: unset;
    line-height: 1;
    margin-bottom: -6px;
  }
  .single-blog-box {
    padding: 15px;
  }
  .single-blog-box img {
    margin-bottom: 15px;
  }
  .single-blog-box h3 {
    margin-bottom: 10px;
    font-size: 22px;
  }
  .single-blog-box p {
    margin-bottom: 12px;
  }
  .single-footer-widget h3 {
    font-size: 22px;
  }
  /* Home Two */
  .header-area.header-area-style-two {
    position: unset;
  }
  .banner-area.banner-area-style-two {
    height: 100%;
  }
  .banner-area.banner-area-style-two::before {
    display: none;
  }
  .banner-area.banner-area-style-two .banner-content {
    margin-top: 0;
  }
  .banner-area.banner-area-style-two .banner-content h1 {
    color: #1f2428;
  }
  .single-success {
    padding-right: 0;
    padding-left: 0;
  }
  .choose-img {
    text-align: center;
  }
  .choose-img img {
    width: 100%;
  }
  .choose-img .your-of {
    position: unset;
    max-width: 100%;
    margin-top: 30px;
  }
  .faq-accordion h2 {
    font-size: 30px;
    text-align: center;
    margin-bottom: 30px;
  }
  .logistics-solutions-area.logistics-solutions-area-style-two .logistics-solutions-img.bg-2 .video-button {
    bottom: -190px;
    right: 30px;
  }
  .logistics-solutions-img.bg-2 {
    margin-bottom: 0;
    margin-top: 30px;
  }
  .testimonials-img {
    margin-bottom: 15px;
  }
  .single-testimonials-box {
    padding: 20px;
  }
  .single-testimonials-box ul {
    position: unset;
    margin-bottom: 10px;
  }
  .testimonials-area-style-two .owl-dots {
    margin-top: 0 !important;
  }
  .request-quote-area.request-quote-area-style-two .request-quote-content {
    padding: 20px;
  }
  .request-quote-area.request-quote-area-style-two .request-quote-content .request-quote-from {
    box-shadow: none;
    background-color: transparent;
  }
  .faq-accordion .accordion .accordion-item {
    padding-left: 40px;
  }
  .faq-accordion .accordion .accordion-title i {
    left: -24px;
  }
  /* Home Three */
  .header-area.header-area-style-three {
    position: unset;
  }
  .navbar-area.navbar-area-style-three {
    border-bottom: 1px solid #e5e9ff;
  }
  .banner-area.banner-area-style-three {
    position: relative;
    z-index: 1;
  }
  .banner-area.banner-area-style-three::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    opacity: 0.6;
    z-index: -1;
  }
  .about-us-content {
    text-align: center;
  }
  .about-us-content h2 {
    font-size: 30px;
    margin-bottom: 20px;
  }
  .about-us-content .default-btn {
    margin-top: 10px;
  }
  .logistics-solutions-area.logistics-solutions-area-style-three .logistics-solutions ul li {
    padding-right: 0;
  }
  .logistics-solutions-area.logistics-solutions-area-style-three .logistics-solutions ul li i::after {
    right: 0;
    left: 0;
    margin: auto;
  }
  .single-pricing-box .pricing-title h3 {
    font-size: 22px;
  }
  .single-pricing-box .pricing-title h1 {
    font-size: 40px;
  }
  .single-pricing-box .pricing-title h1 sub span {
    font-size: 25px;
  }
  .blog-area.blog-area-style-three .single-blog-box .single-blog-content {
    padding: 15px;
  }
  /* Home Four */
  .header-area.header-area-style-four {
    position: unset;
  }
  .banner-area.banner-area-style-four .banner-content h1 {
    color: #ffffff;
  }
  /* Inner Page */
  .page-title-area {
    padding-top: 60px;
    padding-bottom: 60px;
    position: relative;
    z-index: 1;
    text-align: center;
  }
  .page-title-area::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    opacity: 0.7;
  }
  .page-title-area .page-title-content h2 {
    font-size: 35px;
    margin-bottom: 5px;
  }
  .services-details-content.content-1 h3 {
    font-size: 25px;
  }
  .services-details-content.content-3 h3 {
    font-size: 22px;
  }
  .services-details-content.content-4 h3 {
    font-size: 22px;
  }
  .services-details-content.content-5 h3 {
    font-size: 22px;
  }
  .sidebar-widget {
    margin-right: 0;
  }
  .sidebar-widget h3 {
    font-size: 22px;
  }
  .sidebar-widget.recent-post ul li {
    height: 100px;
  }
  .sidebar-widget.recent-post ul li:last-child {
    padding-bottom: 0 !important;
  }
  .user-area .log-in-50 {
    margin-bottom: 50px;
  }
  .coming-soon-content h1 {
    font-size: 60px;
  }
  .error-area {
    padding: 70px 0;
    height: 100%;
  }
  .error-area .error-content p {
    margin: 15px 0 20px;
    font-size: 15px;
  }
  .error-area .error-content h1 {
    font-size: 180px;
    top: -35px;
  }
  .pagination-area {
    margin-bottom: 35px;
  }
  .blog-details-content.content-3 h3 {
    font-size: 25px;
  }
  .blog-details-content.content-4 blockquote {
    padding: 20px;
  }
  .blog-details-content.content-4 blockquote i {
    display: none;
  }
  .blog-details-content.content-4 blockquote p {
    font-size: 16px;
  }
  .blog-details-content.content-6 {
    text-align: center;
  }
  .blog-details-content.content-6 ul li span {
    font-size: 15px;
  }
  .blog-details-content.content-6 ul li a {
    margin-right: 3px;
  }
  .blog-details-content.content-6 .social-icon {
    float: unset;
    margin-top: 10px;
  }
  .blog-details-content.content-6 .social-icon li span {
    font-size: 15px;
  }
  .blog-details-content.content-7 {
    text-align: center;
    padding: 20px;
    padding-right: 20px;
  }
  .blog-details-content.content-7 img {
    position: unset;
    margin-bottom: 20px;
  }
  .blog-details-content.content-7 h3 {
    font-size: 22px;
  }
  .blog-details-content.content-8 {
    margin-bottom: 35px;
  }
  .blog-details-content.content-8 h3 {
    font-size: 22px;
  }
  .single-contact-info::before {
    display: none;
  }
  .single-contact-info .count-title {
    margin-right: 0;
  }
  .single-contact-info i {
    position: unset;
    display: block;
    margin-bottom: 15px;
    text-align: center;
  }
  .main-contact-area #contactForm {
    padding: 20px;
  }
  .main-contact-area .form-group {
    margin-bottom: 20px;
  }
  .sidebar-widget.recent-post {
    margin-bottom: 15px;
  }
  .about-us-content {
    margin: auto;
  }
  .about-area .container-fluid {
    max-width: 720px;
  }
  .single-testimonials-box {
    text-align: center;
  }
  .single-testimonials-box .testimonials-img {
    margin-bottom: 12px;
  }
  .single-testimonials-box .testimonials-img .testimonials-mane {
    position: unset;
    margin-top: 15px;
  }
  .testimonials-area-style-two .owl-carousel .owl-item img {
    margin: auto;
  }
  .single-testimonials p {
    font-size: 16px;
  }
}
/* Min width 992px to Max width 1199px */
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .desktop-nav .navbar .navbar-nav {
    margin-right: auto;
  }
  .desktop-nav .navbar .navbar-nav .nav-item a {
    font-size: 14px;
    margin-left: 9px;
    margin-right: 9px;
  }
  .desktop-nav .navbar .others-options ul li .default-btn {
    font-size: 15px;
    padding: 10px 10px;
  }
  .banner-content {
    margin: auto;
    text-align: center;
  }
  .banner-content h1 {
    color: #ffffff;
  }
  .banner-content p {
    color: #ffffff;
  }
  .banner-area {
    height: 100%;
    z-index: 1;
    padding-top: 100px;
    padding-bottom: 100px;
    background-position: center center;
    background-size: cover;
  }
  .banner-area::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: #000000;
    opacity: 0.9;
    z-index: -1;
  }
  .about-year-content {
    margin-right: 0;
    text-align: center;
  }
  .tabs li {
    font-size: 14px;
    padding: 10px 20px;
  }
  .single-services-box {
    padding: 15px;
  }
  .single-services-box img {
    margin-bottom: 15px;
  }
  .single-services-box h3 {
    font-size: 18px;
  }
  .single-services-box p {
    font-size: 15px;
  }
  .single-counter .count-title h2 {
    font-size: 45px;
  }
  .single-counter .count-title h2 .target {
    font-size: 45px;
    margin-right: -8px;
  }
  .single-counter .count-title h4 {
    font-size: 18px;
  }
  .single-counter::before {
    width: 150px;
    height: 148px;
  }
  /* Home Two */
  .banner-area.banner-area-style-two::before {
    display: none;
  }
  .banner-area.banner-area-style-two .banner-content h1 {
    color: #1f2428;
  }
  .single-success {
    padding-right: 15px;
    padding-left: 15px;
  }
  /* Home Three */
  .banner-area.banner-area-style-three {
    position: relative;
    z-index: 1;
  }
  .banner-area.banner-area-style-three::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    opacity: 0.6;
    z-index: -1;
  }
  /* Home Four */
  .banner-area.banner-area-style-four .banner-content h1 {
    color: #ffffff;
  }
  /* Inner Page */
  .services-details-area .sidebar-widget {
    margin-left: 0;
  }
  .single-team-member {
    padding: 15px;
    padding-bottom: 0;
  }
  .single-team-member .team-content {
    padding: 15px;
  }
  .single-team-member .team-content h3 {
    margin-top: -6px;
    font-size: 17px;
  }
  .single-team-member .team-content .team-social ul {
    top: -158px;
  }
  .single-team-member .team-content .team-social ul li a i {
    width: 30px;
    height: 30px;
    line-height: 29px;
    font-size: 20px;
  }
  .single-team-member .team-content .team-social .control {
    width: 30px;
    height: 30px;
    line-height: 30px;
    top: -38px;
  }
  .sidebar-widget {
    margin-right: 0;
  }
  .blog-details-content.content-6 ul li span {
    font-size: 15px;
  }
  .blog-details-content.content-6 ul li a {
    margin-right: 3px;
  }
  .single-contact-info .count-title p {
    font-size: 15px;
  }
}
/* Min width 1200px to Max width 1399px */
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .desktop-nav .navbar .navbar-nav .nav-item a {
    margin-left: 12px;
    margin-right: 12px;
  }
}
/* Max width 1700px */
@media only screen and (min-width: 1700px) {
  .banner-area {
    height: 795px;
  }
  .banner-area.banner-area-style-two {
    height: 100vh;
  }
}
/* Mobile and iPad Navbar */
@media only screen and (max-width: 991px) {
  .navbar-area {
    background-color: #ffffff;
    padding-top: 15px;
    padding-bottom: 15px;
    z-index: 2;
  }
  .navbar-area.is-sticky {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  .mobile-responsive-nav {
    display: block;
  }
  .mobile-responsive-nav .mobile-responsive-menu {
    position: relative;
  }
  .mobile-responsive-nav .mobile-responsive-menu.mean-container .mean-nav {
    margin-top: 52px;
    background-color: #ffffff;
  }
  .mobile-responsive-nav .mobile-responsive-menu.mean-container .mean-nav ul {
    font-size: 15px;
    border: none !important;
  }
  .mobile-responsive-nav .mobile-responsive-menu.mean-container .mean-nav ul li a {
    color: #000000;
    border-top-color: #DBEEFD;
    text-transform: capitalize;
  }
  .mobile-responsive-nav .mobile-responsive-menu.mean-container .mean-nav ul li a i {
    display: none;
  }
  .mobile-responsive-nav .mobile-responsive-menu.mean-container .mean-nav ul li a.mean-expand {
    width: 100%;
    height: 28px;
    text-align: left;
    padding: 11px !important;
    background: transparent !important;
    border-right: none !important;
    border-bottom: none !important;
  }
  .mobile-responsive-nav .mobile-responsive-menu.mean-container .mean-nav ul li a.active {
    color: #f76649;
  }
  .mobile-responsive-nav .mobile-responsive-menu.mean-container .mean-nav ul li li a {
    font-size: 15px;
  }
  .mobile-responsive-nav .mobile-responsive-menu.mean-container .navbar-nav {
    height: 318px;
    overflow-y: scroll;
    box-shadow: 0 7px 13px 0 rgba(0, 0, 0, 0.1);
  }
  .mobile-responsive-nav .mean-container a.meanmenu-reveal {
    top: 0;
    padding: 0;
    width: 35px;
    height: 30px;
    padding-top: 7px;
    color: #f76649;
  }
  .mobile-responsive-nav .mean-container a.meanmenu-reveal span {
    background: #f76649;
    height: 4px;
    margin-top: -6px;
    border-radius: 3px;
    position: relative;
    top: 8px;
  }
  .mobile-responsive-nav .mean-container .mean-bar {
    background: transparent;
    position: absolute;
    z-index: 999;
    padding: 0;
  }
  .mobile-responsive-nav .others-options {
    display: none !important;
  }
  .mobile-responsive-nav .logo {
    position: relative;
    width: 130px;
    z-index: 999;
  }
  .desktop-nav {
    display: none;
  }
  .others-option-for-responsive {
    display: block;
  }
  .mobile-responsive-nav .mean-container .navbar-nav .nav-item.mega-menu .dropdown-menu .nav-item .row {
    display: block;
    flex-wrap: unset;
    margin-right: 0;
    margin-left: 0;
    margin-top: -20px;
  }
  .mobile-responsive-nav .mean-container .navbar-nav .nav-item.mega-menu .dropdown-menu .nav-item .row .col {
    flex-basis: unset;
    flex-grow: unset;
    max-width: 100%;
    padding-right: 0;
    padding-left: 0;
    padding-top: 20px;
  }
  .mobile-responsive-nav .mean-container .navbar-nav .nav-item.mega-menu .dropdown-menu .nav-item .row .col .sub-menu-title {
    padding: 1em 10%;
  }
  .mobile-responsive-nav .mean-container .navbar-nav .nav-item.mega-menu .dropdown-menu .sub-menu {
    width: auto;
    overflow: hidden;
    display: block !important;
    padding-right: 0;
    padding-left: 0;
  }
  .mobile-responsive-nav .mean-container .navbar-nav .nav-item.mega-menu .dropdown-menu .sub-menu li a {
    padding: 1em 10%;
  }
  .mobile-responsive-nav .mean-container .navbar-nav .nav-item.mega-menu .dropdown-menu .sub-menu li:last-child a {
    padding-bottom: 0;
  }
  .others-option-for-responsive .others-option ul {
    line-height: 1;
    top: -1px;
    position: relative;
  }
  .others-option-for-responsive .others-option ul li .default-btn {
    padding: 15px 33px;
  }
  .others-option-for-responsive .others-option ul li .option-item .search-overlay.search-popup {
    left: auto;
    right: -10px;
    width: 290px;
  }
}
/*Continuer Custom Width Area Style*/
@media only screen and (min-width: 1300px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl {
    max-width: 1320px;
  }
}/*# sourceMappingURL=responsive.css.map */