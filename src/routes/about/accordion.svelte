<script lang="ts">
	let activeIndex = 0;

	const items = [
		{
			title: 'تاریخ ما',
			icon: 'flaticon-contract',
			content: `
			<p>شرکت حمل‌ونقل بین‌المللی SSE فعالیت خود را از سال‌ها پیش با هدف ارائه خدمات لجستیکی سریع و قابل اعتماد آغاز کرد.</p>
			<p>در طول مسیر، با گسترش ناوگان و توسعه شبکه‌های حمل‌ونقل زمینی، هوایی و دریایی، توانسته‌ایم پاسخگوی نیاز مشتریان در بازارهای داخلی و بین‌المللی باشیم.</p>
		`
		},
		{
			title: 'چشم انداز ما',
			icon: 'flaticon-target',
			content: `
			<p>هدف ما تبدیل شدن به یکی از پیشگامان صنعت حمل‌ونقل و لجستیک در سطح منطقه‌ای و جهانی است.</p>
			<p>با تمرکز بر نوآوری، دیجیتالی‌سازی فرآیندها و بهبود مداوم خدمات، به دنبال ارائه تجربه‌ای متفاوت و حرفه‌ای برای مشتریان خود هستیم.</p>
		`
		},
		{
			title: 'ماموریت ما',
			icon: 'flaticon-mission',
			content: `
			<p>ماموریت SSE ارائه خدمات حمل‌ونقل بین‌المللی با سرعت، دقت و امنیت بالا است.</p>
			<p>ما متعهد هستیم که با تکیه بر تخصص تیم خود، پشتیبانی کامل از زنجیره تأمین مشتریان را فراهم کنیم و فرآیندهای صادرات و واردات را تسهیل نماییم.</p>
		`
		}
	];
	function toggle(index: number) {
		activeIndex = index === activeIndex ? -1 : index;
	}
</script>

<div class="faq-accordion">
	<h2>چرا باید خدمات حمل و نقل و تدارکات SSE را انتخاب کنید</h2>
	<ul class="accordion">
		{#each items as item, index}
			<li class="accordion-item">
				<a class="accordion-title {activeIndex === index ? 'active' : ''}">
					<span class={item.icon}></span>
					{item.title}
					<i class="ri-add-fill"></i>
				</a>
				<div class="accordion-content {activeIndex === index ? 'show' : ''}">
					{@html item.content}
				</div>
			</li>
		{/each}
	</ul>
</div>

<style>
	.accordion-content {
		display: none;
	}
	.accordion-content.show {
		display: block;
	}
</style>
